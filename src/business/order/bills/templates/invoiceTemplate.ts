import { TEMPLATE_VARIABLE } from '../constants/template-variables.constant';
import { buildingPrimarySVG, rightSVG, userPrimarySVG } from '../images/icons';

export const generateInvoiceHTMLTemplate = () => `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Invoice</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
     /* Base colors */
  .text-black-text { color: #20363f; }
  .text-text-color { color: #090a1a; }
  .bg-disabled { background-color: rgba(0, 0, 0, 0.04); }

  /* Grays */
  .bg-grey-25 { background-color: #fcfcfd; }
  .bg-grey-50 { background-color: #f9fafb; }
  .border-grey-200 { border-color: #e4e7ec; }
  .text-grey-500 { color: #667085; }

  /* Primary */
  .bg-primary-25 { background-color: #f5f6ff; }
  .bg-primary-50 { background-color: #e3e5f6; }
  .bg-primary-500 { background-color: #2d3484; }
  .text-primary-900 { color: #090a1a; }
  .text-primary-300 { color: #767bb2; }
  .border-primary-50 { border-color: #e3e5f6; }

  /* Secondary */
  .text-secondary-600 { color: #ef5c26; }

  /* Success */
  .bg-success-50 { background-color: #ecfdf3; }
  .text-success-600 { color: #039855; }

  /* Error */
  .bg-error-50 { background-color: #fef3f2; }
  .text-error-600 { color: #d92d20; }

  /* Warning */
  .bg-warning-50 { background-color: #fffaeb; }
  .text-warning-600 { color: #dc6803; }

  /* Custom font */
  .font-inter { font-family: Inter, sans-serif; }

  /* Table styling */
  .template-table th {
    border-bottom: 1px solid #e3e5f6;
    padding: 12px 24px;
    color: #090a1a;
    font-weight: 600;
    text-align: left;
  }
  .template-table th:not(:last-child) {
    border-right: 1px solid #e3e5f6;
  }
  .template-table td {
    padding: 4px 24px;
  }
  .template-table td:not(:last-child) {
    border-right: 1px solid #e3e5f6;
  }
  .template-table tbody tr:nth-child(even) {
    background-color: #f5f6ff;
  }

    </style>
  </head>
  <body class="bg-white min-h-screen p-6 text-primary-900 w-full">
    <div
      class="max-w-4xl mx-auto rounded-lg overflow-hidden px-10 py-8 relative"
      style="box-shadow: 0px 10px 20px 0px rgba(37, 49, 76, 0.15);"
    >
      <img
        src="./assets/InvoiceTemplateShadder.png"
        alt=""
        class="absolute left-0 top-0 mix-blend-multiply z-10"
      />
      <div class="flex justify-between items-center mb-6 gap-4 relative z-50">
        <div>
          <img src="./assets/LumigoLogo.png" alt="Lumigo Logo" class="h-8 mb-2" />
          <p class="text-sm leading-5">
            Lumigo Solution 2025 Inc (Lumigo Transport) <br />
            Phone: ************ Website: www.lumigotransport.ca
          </p>
        </div>
        <div class="text-left pl-5 border-l border-l-gary-300">
          <div class="text-sm text-primary-200">Invoice number:</div>
          <div class="font-semibold text-sm mb-2">INV000027</div>
          <div class="text-sm text-primary-200">Issued:</div>
          <div class="font-semibold text-sm">June 26, 2024</div>
        </div>
      </div>

      <div class="flex gap-2.5 bg-white text-sm border border-primary-50 p-6 rounded-lg relative z-50">
        <!-- BILL TO -->
        <div>
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
           <svg width="14" height="14" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 6C12 9.3135 9.3135 12 6 12C2.6865 12 0 9.3135 0 6C0 2.6865 2.6865 0 6 0C9.3135 0 12 2.6865 12 6Z"
        fill="#2D3484"
      />
      <path
        d="M9.17568 3.17542L5.10468 7.25121L3.42408 5.57541L2.57568 6.42381L5.10588 8.94801L10.0244 4.02381L9.17568 3.17542Z"
        fill="white"
      />
    </svg> ${rightSVG} BILL TO:
          </h3>
          <div class="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]">
            <p class="font-medium text-gray-800 mb-4 text-sm"> ${userPrimarySVG} Mauro Sicard</p>
            <p class="mb-2">(612) 865 - 0989</p>
            <p class="mb-2"><EMAIL></p>
            <p>Palo Alto, San Francisco, CA 92102, United States of America</p>
          </div>
        </div>

        <!-- BILL FROM -->
        <div>
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
            ${rightSVG}BILL FROM:
          </h3>
          <div class="bg-primary-25 border border-primary-50 rounded-lg p-6 text-primary-300 text-xs h-[calc(100%-24px)]">
            <p class="font-medium text-gray-800 mb-4 text-sm">${buildingPrimarySVG} BRIX Agency</p>
            <p class="mb-2">(684) 879 - 0102</p>
            <p class="mb-2">Palo Alto, San Francisco, CA 94110, United States of America</p>
            <p class="mb-2"></p>
            <p>12345 6789 US0001</p>
          </div>
        </div>

        <!-- AMOUNT DUE -->
        <div class="h-full">
          <h3 class="font-semibold mb-2 text-xs flex gap-2 items-center">
            ${rightSVG} AMOUNT DUE:
          </h3>
          <div class="border border-primary-50 rounded-lg p-6 px-7 bg-primary-500 text-primary-300 h-[calc(100%-22px)]">
            <p class="text-sm text-white mb-1">CAD</p>
            <p class="text-2xl font-bold text-white mb-2">$19,570.00</p>
            <p class="text-sm text-white mb-2">July 26, 2024</p>
            <span class="block w-fit text-green-600 font-semibold success-chip !px-2 !py-1">
              Paid
            </span>
          </div>
        </div>
      </div>

      <!-- TABLE -->
      <div class="mt-8 overflow-auto border border-primary-50 rounded-lg">
        <table class="template-table min-w-full text-sm text-left !text-primary-900">
          <thead>
            <tr class="bg-primary-25">
              <th>Tracking Number</th>
              <th>Service Level</th>
              <th>Completed Date</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
          ${TEMPLATE_VARIABLE.INVOICE_ORDERS}
           
            <tr class="border-t"><td>5637657</td><td>4hr service</td><td>30/01/2020 04:00 PM</td><td>$40,000</td></tr>
            <tr class="border-t"><td>8656436</td><td>2 hr service</td><td>19/07/2020 10:00 AM</td><td>$50,000</td></tr>
            <tr class="border-t"><td>9002984</td><td>4 hr service</td><td>31/03/2020 11:30 AM</td><td>$80,000</td></tr>
            <tr class="border-t"><td>5236852</td><td>Next day service</td><td>27/02/2020 06:00 PM</td><td>$60,000</td></tr>
            <tr class="border-t"><td>3342765</td><td>Express 2hr service</td><td>09/04/2020 03:30 AM</td><td>$70,000</td></tr>
            <tr class="border-t"><td>5236850</td><td>2 hr service</td><td>12/06/2020 04:30 PM</td><td>$100,000</td></tr>
            <tr class="border-t"><td>7523765</td><td>2 hr service</td><td>26/04/2020 11:30 AM</td><td>$40,000</td></tr>
            <tr class="border-t"><td>1283746</td><td>Same day service</td><td>15/05/2020 01:00 PM</td><td>$55,000</td></tr>
            <tr class="border-t"><td>6482736</td><td>Standard delivery</td><td>20/05/2020 02:30 PM</td><td>$30,000</td></tr>
            <tr class="border-t"><td>2938475</td><td>Overnight service</td><td>10/06/2020 08:00 PM</td><td>$90,000</td></tr>
            <tr class="border-t"><td>9837462</td><td>Express 1hr service</td><td>18/06/2020 06:15 PM</td><td>$110,000</td></tr>
            <tr class="border-t"><td>3847562</td><td>Weekend delivery</td><td>22/06/2020 07:45 AM</td><td>$65,000</td></tr>
            <tr class="border-t"><td>8473629</td><td>4hr service</td><td>25/06/2020 09:00 AM</td><td>$45,000</td></tr>
            <tr class="border-t"><td>1938476</td><td>Next day service</td><td>28/06/2020 10:30 AM</td><td>$50,000</td></tr>
            <tr class="border-t"><td>8472635</td><td>2 hr service</td><td>30/06/2020 03:00 PM</td><td>$35,000</td></tr>
          </tbody>
        </table>
      </div>

      <!-- SUMMARY -->
      <div class="grid grid-cols-2 gap-3 mt-8 text-sm relative z-50">
        <div class="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24">
          <div class="flex justify-between items-center">
            <p class="font-medium">Total Orders</p>
            <p class="font-bold">16</p>
          </div>
          <div class="flex justify-between mt-1 items-center">
            <p class="font-medium mt-2">Balance</p>
            <p class="font-bold">$0.00</p>
          </div>
        </div>
        <div class="bg-primary-25 border border-primary-50 p-4 rounded-lg min-h-24">
          <p class="flex justify-between items-center">
            Sub total <span class="font-bold">$100.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            GST <span class="font-bold">$3.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            QST <span class="font-bold">$9.00</span>
          </p>
          <p class="flex justify-between items-center mt-2">
            Total amount <span class="font-bold">$112.00</span>
          </p>
        </div>
      </div>

      <!-- TERMS -->
      <div class="mt-6 text-xs text-primary-900 leading-[18px]">
        <p>
          <span class="font-bold text-sm">Terms & Conditions:</span>
        </p>
        <p>
          Fees and payment terms will be established in the contract or agreement prior to the
          commencement of the project. An initial deposit will be required before any design work
          begins. We reserve the right to suspend or halt work in the event of non-payment.
        </p>
      </div>
    </div>
  </body>
</html>
`;
