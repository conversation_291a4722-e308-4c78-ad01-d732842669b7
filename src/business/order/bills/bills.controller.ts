import {
  <PERSON>,
  Get,
  UseGuards,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>q,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCookieAuth,
  ApiProduces,
} from '@nestjs/swagger';
import { BillsService } from './bills.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { Response } from 'express';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { JwtPayload } from '../../../core/auth/domain/auth.types';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { OrderBills } from './constants/template-variables.constant';

@ApiTags('Business - Order - Bills')
@ApiBearerAuth()
@ApiC<PERSON><PERSON><PERSON><PERSON>()
@Controller({
  path: '/orders',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class BillsController {
  constructor(private readonly billsService: BillsService) {}

  @Get(':orderId/bills/:billType')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async getBill(
    @CurrentUser() contactData: JwtPayload,
    @Param('orderId') orderId: string,
    @Param('billType') billType: OrderBills,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ): Promise<any> {
    const tenantId = contactData.ctx.tenantId;

    if (!tenantId) {
      throw new Error('Tenant id not found');
    }

    const pdfBuffer = await this.billsService.getBill(
      orderId,
      billType,
      tenantId,
    );
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${billType}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }

  @Get('invoice/:invoiceId')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async printInvoice(
    @CurrentUser() contactData: JwtPayload,
    @Param('orderId') orderId: string,
    @Param('billType') billType: OrderBills,
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ): Promise<any> {
    const tenantId = contactData.ctx.tenantId;

    if (!tenantId) {
      throw new Error('Tenant id not found');
    }

    const pdfBuffer = await this.billsService.getBill(
      orderId,
      billType,
      tenantId,
    );
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${billType}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }
}
