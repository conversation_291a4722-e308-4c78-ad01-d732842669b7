import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
  HttpStatus,
} from '@nestjs/common';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { billOfLadingHTMLTemplate } from './templates/billOfLading';
import { shippingLabelHTMLTemplate } from './templates/shippingLabel';
import { wayBillHTMLTemplate } from './templates/wayBill';
import { OrdersService } from '../orders/orders.service';
import { TenantsService } from '../../user/tenants/tenants.service';
import { AppException } from '../../../utils/errors/app.exception';
import { ErrorCode } from '../../../utils/errors/error-codes';
import { TemplateParser } from './utils/template-parser.util';
import { OrderBills } from './constants/template-variables.constant';
import { generateInvoiceHTMLTemplate } from './templates/invoiceTemplate';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);

  constructor(
    private readonly puppeteerService: PuppeteerService,
    private readonly orderService: OrdersService,
    private readonly tenantsService: TenantsService,
  ) {}

  async onModuleInit() {
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
    this.logger.log('BillsService initialized with TypeScript templates');
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  async getBill(
    orderId: string,
    billName: OrderBills,
    tenantId: string,
  ): Promise<Buffer> {
    try {
      const order = await this.orderService.findOne(tenantId, orderId);

      if (!order) {
        throw new AppException(
          `Order with ID ${orderId} not found`,
          ErrorCode.ORDER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      // Retrieve tenant information for template variables
      const tenant = await this.tenantsService.findById(tenantId);

      if (!tenant) {
        throw new AppException(
          `Tenant with ID ${tenantId} not found`,
          ErrorCode.TENANT_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      let html: string | null;

      // Generate HTML based on the bill type
      switch (billName) {
        case OrderBills.BILL_OF_LADING:
          html = billOfLadingHTMLTemplate;
          break;
        case OrderBills.WAY_BILL:
          html = wayBillHTMLTemplate;
          break;
        case OrderBills.SHIPPING_LABEL:
          html = shippingLabelHTMLTemplate;
          break;
        default:
          html = null;
      }

      if (!html) {
        throw new AppException(
          `Bill type ${billName} not supported`,
          ErrorCode.BILL_TYPE_NOT_SUPPORTED,
          HttpStatus.NOT_FOUND,
        );
      }

      let options: any = {};

      if (billName === OrderBills.SHIPPING_LABEL) {
        options = {
          format: '',
          width: '600px',
          height: '8in',
          margin: { top: '0px', right: '0px', bottom: '0px', left: '0px' },
        };
      }

      html = TemplateParser.parseTemplate(html, { order, tenant });

      // Generate PDF using PuppeteerService
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(
        html,
        options,
      );
      return pdfBuffer;
    } catch (error) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw error;
    }
  }

  async printInvoice(orderId: string, tenantId: string): Promise<Buffer> {
    try {
      const filter = new BaseFilterDto();
      const orders = await this.orderService.findAll(tenantId, filter);

      if (!orders) {
        throw new AppException(
          `Order with ID ${orderId} not found`,
          ErrorCode.ORDER_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      // Retrieve tenant information for template variables
      const tenant = await this.tenantsService.findById(tenantId);

      if (!tenant) {
        throw new AppException(
          `Tenant with ID ${tenantId} not found`,
          ErrorCode.TENANT_NOT_FOUND,
          HttpStatus.NOT_FOUND,
        );
      }

      let html = generateInvoiceHTMLTemplate();
      html = TemplateParser.parseTemplate(html, {
        invoice: { orders: orders.data },
        tenant,
      });

      // Generate PDF using PuppeteerService
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error) {
      this.logger.error(
        'Failed to generate invoice PDF',
        error?.stack || error,
      );
      throw error;
    }
  }
}
