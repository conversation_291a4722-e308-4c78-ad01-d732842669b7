import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common';
import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';

@Injectable()
export class PuppeteerService implements OnModuleDestroy {
  private browser: Browser | null = null;

  async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
    }
    return this.browser;
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async generatePDFFromHTML(
    htmlContent: string,
    options?: any,
  ): Promise<Buffer> {
    const browser = await this.initBrowser();
    const page = await browser.newPage();
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 });

      // Use 'domcontentloaded' for faster rendering since we have inline CSS
      await page.setContent(htmlContent, {
        waitUntil: 'domcontentloaded',
        timeout: 10000, // Reduced timeout
      });

      await page.evaluateHandle('document.fonts.ready');
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images).map((img) => {
            if (img.complete) return Promise.resolve();
            return new Promise((resolve) => {
              img.onload = resolve;
              img.onerror = resolve;
            });
          }),
        );
      });

      // Wait a bit for fonts to render properly
      await new Promise((resolve) => setTimeout(resolve, 500));

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' },
        displayHeaderFooter: false,
        ...options,
      });
      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  async onModuleDestroy() {
    await this.closeBrowser();
  }
}
